const fs = require('fs');

// 读取混淆的代码
const obfuscatedCode = fs.readFileSync('extension/dist/extension.js', 'utf8');

console.log('=== 提取插件逻辑分析 ===');

// 1. 查找可能的主要导出函数
function findMainFunction(code) {
    console.log('\n--- 主函数分析 ---');
    
    // 查找 activate 函数相关的模式
    const activatePatterns = [
        /activate[^{]*{[^}]*}/gi,
        /function\s+activate/gi,
        /exports\.activate/gi,
        /module\.exports\s*=\s*{[^}]*activate[^}]*}/gi
    ];
    
    activatePatterns.forEach((pattern, index) => {
        const matches = code.match(pattern);
        if (matches) {
            console.log(`激活函数模式 ${index + 1}:`, matches);
        }
    });
    
    // 查找可能的导出对象
    const exportPattern = /(?:exports|module\.exports)\s*=\s*{[^}]+}/g;
    const exportMatches = code.match(exportPattern);
    if (exportMatches) {
        console.log('\n导出对象:');
        exportMatches.forEach((exp, index) => {
            console.log(`  导出 ${index + 1}:`, exp.substring(0, 200) + '...');
        });
    }
}

// 2. 查找字符串解密函数
function findDecryptionLogic(code) {
    console.log('\n--- 解密逻辑分析 ---');
    
    // 查找可能的字符串解密函数
    const decryptPatterns = [
        /function\s+_0x[a-f0-9]+\([^)]*\)\s*{[^}]*charCodeAt[^}]*}/gi,
        /function\s+_0x[a-f0-9]+\([^)]*\)\s*{[^}]*fromCharCode[^}]*}/gi,
        /function\s+_0x[a-f0-9]+\([^)]*\)\s*{[^}]*String\[['"]fromCharCode['"]\][^}]*}/gi
    ];
    
    decryptPatterns.forEach((pattern, index) => {
        const matches = code.match(pattern);
        if (matches) {
            console.log(`解密函数模式 ${index + 1}:`, matches.length, '个匹配');
            matches.slice(0, 2).forEach((match, matchIndex) => {
                console.log(`    函数 ${matchIndex + 1}:`, match.substring(0, 150) + '...');
            });
        }
    });
}

// 3. 查找可能的恶意行为
function findMaliciousBehavior(code) {
    console.log('\n--- 恶意行为分析 ---');
    
    // 查找可能的数据收集
    const dataCollectionPatterns = [
        /machine.*id/gi,
        /hardware/gi,
        /serial/gi,
        /uuid/gi,
        /mac.*address/gi,
        /computer.*name/gi,
        /user.*name/gi,
        /home.*dir/gi,
        /process\.env/gi
    ];
    
    console.log('数据收集模式:');
    dataCollectionPatterns.forEach((pattern, index) => {
        const matches = code.match(pattern);
        if (matches) {
            console.log(`  模式 ${index + 1} (${pattern}):`, [...new Set(matches)]);
        }
    });
    
    // 查找网络通信
    const networkPatterns = [
        /http[s]?:\/\/[^\s'"]+/gi,
        /fetch\s*\(/gi,
        /XMLHttpRequest/gi,
        /\.send\s*\(/gi,
        /\.post\s*\(/gi,
        /\.get\s*\(/gi,
        /request\s*\(/gi
    ];
    
    console.log('\n网络通信模式:');
    networkPatterns.forEach((pattern, index) => {
        const matches = code.match(pattern);
        if (matches) {
            console.log(`  模式 ${index + 1} (${pattern}):`, [...new Set(matches)]);
        }
    });
    
    // 查找文件操作
    const filePatterns = [
        /writeFile/gi,
        /readFile/gi,
        /createWriteStream/gi,
        /createReadStream/gi,
        /mkdir/gi,
        /rmdir/gi,
        /unlink/gi,
        /copy/gi
    ];
    
    console.log('\n文件操作模式:');
    filePatterns.forEach((pattern, index) => {
        const matches = code.match(pattern);
        if (matches) {
            console.log(`  模式 ${index + 1} (${pattern}):`, [...new Set(matches)]);
        }
    });
}

// 4. 查找VS Code特定的API调用
function findVSCodeAPICalls(code) {
    console.log('\n--- VS Code API调用分析 ---');
    
    // 查找命令注册
    const commandPatterns = [
        /registerCommand/gi,
        /executeCommand/gi,
        /commands\.register/gi,
        /commands\.execute/gi
    ];
    
    console.log('命令相关:');
    commandPatterns.forEach((pattern, index) => {
        const matches = code.match(pattern);
        if (matches) {
            console.log(`  模式 ${index + 1} (${pattern}):`, matches.length, '次匹配');
        }
    });
    
    // 查找webview相关
    const webviewPatterns = [
        /webview/gi,
        /createWebviewPanel/gi,
        /postMessage/gi,
        /onDidReceiveMessage/gi
    ];
    
    console.log('\nWebview相关:');
    webviewPatterns.forEach((pattern, index) => {
        const matches = code.match(pattern);
        if (matches) {
            console.log(`  模式 ${index + 1} (${pattern}):`, matches.length, '次匹配');
        }
    });
    
    // 查找窗口和通知
    const windowPatterns = [
        /showInformationMessage/gi,
        /showErrorMessage/gi,
        /showWarningMessage/gi,
        /showInputBox/gi,
        /showQuickPick/gi
    ];
    
    console.log('\n窗口和通知:');
    windowPatterns.forEach((pattern, index) => {
        const matches = code.match(pattern);
        if (matches) {
            console.log(`  模式 ${index + 1} (${pattern}):`, matches.length, '次匹配');
        }
    });
}

// 5. 尝试提取可能的URL和域名
function extractURLsAndDomains(code) {
    console.log('\n--- URL和域名提取 ---');
    
    // 查找URL模式
    const urlPattern = /https?:\/\/[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=%]+/gi;
    const urls = code.match(urlPattern);
    if (urls) {
        console.log('发现的URL:');
        [...new Set(urls)].forEach((url, index) => {
            console.log(`  ${index + 1}: ${url}`);
        });
    }
    
    // 查找域名模式
    const domainPattern = /[a-zA-Z0-9\-]+\.[a-zA-Z]{2,}(?:\.[a-zA-Z]{2,})?/gi;
    const domains = code.match(domainPattern);
    if (domains) {
        const filteredDomains = [...new Set(domains)].filter(domain => 
            !domain.includes('_') && 
            !domain.includes('0x') && 
            domain.length > 4 &&
            /^[a-zA-Z0-9\-]+\.[a-zA-Z]{2,}/.test(domain)
        );
        
        if (filteredDomains.length > 0) {
            console.log('\n可能的域名:');
            filteredDomains.slice(0, 20).forEach((domain, index) => {
                console.log(`  ${index + 1}: ${domain}`);
            });
        }
    }
}

// 执行所有分析
findMainFunction(obfuscatedCode);
findDecryptionLogic(obfuscatedCode);
findMaliciousBehavior(obfuscatedCode);
findVSCodeAPICalls(obfuscatedCode);
extractURLsAndDomains(obfuscatedCode);

console.log('\n=== 逻辑提取完成 ===');
