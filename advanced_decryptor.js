const fs = require('fs');

// 读取混淆的代码
const obfuscatedCode = fs.readFileSync('extension/dist/extension.js', 'utf8');

console.log('=== 高级解密器 ===');

// 1. 提取并分析字符串数组函数
function extractStringArrayFunction(code) {
    console.log('\n--- 提取字符串数组函数 ---');
    
    // 查找 _0x1158 函数（字符串数组）
    const arrayFunctionMatch = code.match(/_0x1158\s*=\s*function\s*\(\s*\)\s*{\s*return\s*\[([^\]]+)\]/);
    if (arrayFunctionMatch) {
        console.log('找到字符串数组函数');
        const arrayContent = arrayFunctionMatch[1];
        
        // 提取字符串
        const strings = [];
        const stringMatches = arrayContent.match(/'[^']*'/g);
        if (stringMatches) {
            stringMatches.forEach(str => {
                strings.push(str.slice(1, -1)); // 移除引号
            });
        }
        
        console.log(`提取到 ${strings.length} 个字符串`);
        console.log('前50个字符串:');
        strings.slice(0, 50).forEach((str, index) => {
            if (str.length > 2) {
                console.log(`  ${index}: "${str}"`);
            }
        });
        
        return strings;
    }
    
    return [];
}

// 2. 模拟解密函数
function createDecryptFunction(stringArray) {
    console.log('\n--- 创建解密函数 ---');
    
    // 基于发现的解密逻辑创建解密函数
    function decrypt(index, key) {
        if (!stringArray || stringArray.length === 0) {
            return `ENCRYPTED_${index}_${key}`;
        }
        
        // 简化的解密逻辑
        const adjustedIndex = index - 0x193; // 基于观察到的偏移
        if (adjustedIndex >= 0 && adjustedIndex < stringArray.length) {
            return stringArray[adjustedIndex];
        }
        
        return `UNKNOWN_${index}_${key}`;
    }
    
    return decrypt;
}

// 3. 尝试解密字符串调用
function decryptStringCalls(code, decryptFunc) {
    console.log('\n--- 解密字符串调用 ---');
    
    let decryptedCode = code;
    let decryptionCount = 0;
    
    // 查找所有字符串解密调用
    const callPattern = /_0x[a-f0-9]+\((0x[a-f0-9]+),\s*'([^']+)'\)/g;
    const calls = [...code.matchAll(callPattern)];
    
    console.log(`找到 ${calls.length} 个字符串调用`);
    
    // 尝试解密前100个调用
    calls.slice(0, 100).forEach((match, index) => {
        const fullMatch = match[0];
        const hexIndex = match[1];
        const key = match[2];
        
        try {
            const numIndex = parseInt(hexIndex, 16);
            const decrypted = decryptFunc(numIndex, key);
            
            if (decrypted && !decrypted.startsWith('ENCRYPTED_') && !decrypted.startsWith('UNKNOWN_')) {
                console.log(`  ${index}: ${fullMatch} -> "${decrypted}"`);
                decryptedCode = decryptedCode.replace(fullMatch, `"${decrypted}"`);
                decryptionCount++;
            }
        } catch (e) {
            // 忽略解密错误
        }
    });
    
    console.log(`成功解密 ${decryptionCount} 个字符串`);
    return decryptedCode;
}

// 4. 查找和解密关键功能
function findAndDecryptKeyFunctions(code, decryptFunc) {
    console.log('\n--- 查找关键功能 ---');
    
    // 查找可能的VS Code API调用
    const patterns = [
        /vscode/gi,
        /registerCommand/gi,
        /createWebviewPanel/gi,
        /showInformationMessage/gi,
        /postMessage/gi,
        /require/gi,
        /exports/gi,
        /activate/gi,
        /deactivate/gi
    ];
    
    patterns.forEach(pattern => {
        const matches = code.match(pattern);
        if (matches) {
            console.log(`发现 ${pattern}: ${matches.length} 次`);
        }
    });
    
    // 查找可能的命令字符串
    const commandPatterns = [
        'augment-infinity',
        'inputCDK',
        'activate',
        'webview',
        'command'
    ];
    
    console.log('\n搜索命令字符串:');
    commandPatterns.forEach(cmd => {
        if (code.includes(cmd)) {
            console.log(`发现: "${cmd}"`);
            
            // 查找上下文
            const index = code.indexOf(cmd);
            const start = Math.max(0, index - 200);
            const end = Math.min(code.length, index + cmd.length + 200);
            const context = code.substring(start, end);
            console.log(`  上下文: ${context.replace(/\s+/g, ' ').substring(0, 300)}...`);
        }
    });
}

// 5. 提取模块导出
function extractModuleExports(code) {
    console.log('\n--- 提取模块导出 ---');
    
    // 查找导出模式
    const exportPatterns = [
        /module\.exports\s*=\s*{[^}]*}/g,
        /exports\s*=\s*{[^}]*}/g,
        /exports\.[a-zA-Z_$][a-zA-Z0-9_$]*\s*=/g
    ];
    
    exportPatterns.forEach((pattern, index) => {
        const matches = code.match(pattern);
        if (matches) {
            console.log(`导出模式 ${index + 1}:`);
            matches.forEach((match, matchIndex) => {
                console.log(`  ${matchIndex + 1}: ${match.substring(0, 200)}...`);
            });
        }
    });
    
    // 查找可能的activate函数
    const activatePattern = /activate\s*:\s*function[^}]*}/g;
    const activateMatches = code.match(activatePattern);
    if (activateMatches) {
        console.log('\n发现activate函数:');
        activateMatches.forEach((match, index) => {
            console.log(`  ${index + 1}: ${match.substring(0, 300)}...`);
        });
    }
}

// 6. 分析网络和文件操作
function analyzeNetworkAndFileOps(code) {
    console.log('\n--- 网络和文件操作分析 ---');
    
    // 网络相关
    const networkPatterns = [
        /https?:\/\/[^\s'"]+/gi,
        /fetch\s*\(/gi,
        /XMLHttpRequest/gi,
        /\.post\s*\(/gi,
        /\.get\s*\(/gi,
        /request\s*\(/gi
    ];
    
    console.log('网络操作:');
    networkPatterns.forEach((pattern, index) => {
        const matches = code.match(pattern);
        if (matches) {
            console.log(`  模式 ${index + 1}: ${matches.length} 次匹配`);
            if (pattern.toString().includes('http')) {
                console.log(`    URLs: ${[...new Set(matches)].slice(0, 5)}`);
            }
        }
    });
    
    // 文件操作
    const filePatterns = [
        /fs\.[a-zA-Z]+/gi,
        /readFile/gi,
        /writeFile/gi,
        /existsSync/gi,
        /mkdirSync/gi
    ];
    
    console.log('\n文件操作:');
    filePatterns.forEach((pattern, index) => {
        const matches = code.match(pattern);
        if (matches) {
            console.log(`  模式 ${index + 1}: ${[...new Set(matches)]}`);
        }
    });
    
    // 系统信息收集
    const systemPatterns = [
        /os\.[a-zA-Z]+/gi,
        /process\.[a-zA-Z]+/gi,
        /machine.*id/gi,
        /getMachineId/gi
    ];
    
    console.log('\n系统信息收集:');
    systemPatterns.forEach((pattern, index) => {
        const matches = code.match(pattern);
        if (matches) {
            console.log(`  模式 ${index + 1}: ${[...new Set(matches)]}`);
        }
    });
}

// 7. 生成解密报告
function generateDecryptionReport(originalCode, decryptedCode, stringArray) {
    console.log('\n--- 生成解密报告 ---');
    
    const report = {
        originalLength: originalCode.length,
        decryptedLength: decryptedCode.length,
        stringArraySize: stringArray.length,
        timestamp: new Date().toISOString(),
        findings: {
            obfuscatedVariables: (originalCode.match(/_0x[a-f0-9]+/g) || []).length,
            stringCalls: (originalCode.match(/_0x[a-f0-9]+\(0x[a-f0-9]+,\s*'[^']+'\)/g) || []).length,
            requireCalls: (originalCode.match(/require\s*\(/g) || []).length,
            vscodeAPIs: (originalCode.match(/vscode\./g) || []).length
        }
    };
    
    // 保存解密后的代码
    fs.writeFileSync('fully_decrypted.js', decryptedCode);
    fs.writeFileSync('decryption_report.json', JSON.stringify(report, null, 2));
    
    console.log('解密统计:');
    console.log(`  原始代码长度: ${report.originalLength}`);
    console.log(`  解密后长度: ${report.decryptedLength}`);
    console.log(`  字符串数组大小: ${report.stringArraySize}`);
    console.log(`  混淆变量数量: ${report.findings.obfuscatedVariables}`);
    console.log(`  字符串调用数量: ${report.findings.stringCalls}`);
    console.log(`  Require调用: ${report.findings.requireCalls}`);
    console.log(`  VS Code API: ${report.findings.vscodeAPIs}`);
    
    console.log('\n文件已保存:');
    console.log('  - fully_decrypted.js (解密后的代码)');
    console.log('  - decryption_report.json (解密报告)');
    
    return report;
}

// 执行完整解密流程
console.log('开始高级解密流程...');

// 步骤1: 提取字符串数组
const stringArray = extractStringArrayFunction(obfuscatedCode);

// 步骤2: 创建解密函数
const decryptFunc = createDecryptFunction(stringArray);

// 步骤3: 解密字符串调用
const partiallyDecrypted = decryptStringCalls(obfuscatedCode, decryptFunc);

// 步骤4: 分析关键功能
findAndDecryptKeyFunctions(partiallyDecrypted, decryptFunc);

// 步骤5: 提取模块导出
extractModuleExports(partiallyDecrypted);

// 步骤6: 分析网络和文件操作
analyzeNetworkAndFileOps(partiallyDecrypted);

// 步骤7: 生成最终报告
const report = generateDecryptionReport(obfuscatedCode, partiallyDecrypted, stringArray);

console.log('\n=== 高级解密完成 ===');
