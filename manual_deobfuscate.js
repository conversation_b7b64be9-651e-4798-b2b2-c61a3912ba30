const fs = require('fs');

// 读取混淆的代码
let obfuscatedCode = fs.readFileSync('extension/dist/extension.js', 'utf8');

console.log('=== 手动去混淆分析 ===');

// 1. 提取和分析字符串数组
function extractStringArray(code) {
    console.log('\n--- 字符串数组提取 ---');
    
    // 查找字符串数组定义模式
    const arrayPattern = /_0x[a-f0-9]+\s*=\s*function\s*\(\s*\)\s*{\s*return\s*\[([^\]]+)\]/;
    const match = code.match(arrayPattern);
    
    if (match) {
        console.log('找到字符串数组定义');
        const arrayContent = match[1];
        
        // 提取字符串
        const strings = [];
        const stringMatches = arrayContent.match(/'[^']*'/g);
        if (stringMatches) {
            stringMatches.forEach(str => {
                strings.push(str.slice(1, -1)); // 移除引号
            });
        }
        
        console.log(`提取到 ${strings.length} 个字符串`);
        console.log('前50个字符串:');
        strings.slice(0, 50).forEach((str, index) => {
            if (str.length > 2 && !/^[a-f0-9]+$/i.test(str)) {
                console.log(`  ${index}: "${str}"`);
            }
        });
        
        return strings;
    }
    
    return [];
}

// 2. 查找主要的导出函数
function findMainExports(code) {
    console.log('\n--- 主要导出函数 ---');
    
    // 查找 module.exports 或 exports 的定义
    const exportPatterns = [
        /module\s*\[\s*_0x[a-f0-9]+\([^)]+\)\s*\]\s*=\s*_0x[a-f0-9]+\([^)]+\)/g,
        /exports\s*=\s*{[^}]*}/g,
        /_0x[a-f0-9]+\s*=\s*{[^}]*activate[^}]*}/g
    ];
    
    exportPatterns.forEach((pattern, index) => {
        const matches = code.match(pattern);
        if (matches) {
            console.log(`导出模式 ${index + 1}:`);
            matches.forEach((match, matchIndex) => {
                console.log(`  ${matchIndex + 1}: ${match.substring(0, 150)}...`);
            });
        }
    });
}

// 3. 查找activate和deactivate函数
function findActivationFunctions(code) {
    console.log('\n--- 激活/停用函数 ---');
    
    // 查找包含activate的函数定义
    const activatePattern = /function\s+_0x[a-f0-9]+\([^)]*\)\s*{[^}]*activate[^}]*}/gi;
    const activateMatches = code.match(activatePattern);
    
    if (activateMatches) {
        console.log('找到激活相关函数:');
        activateMatches.forEach((match, index) => {
            console.log(`  激活函数 ${index + 1}: ${match.substring(0, 200)}...`);
        });
    }
    
    // 查找包含deactivate的函数定义
    const deactivatePattern = /function\s+_0x[a-f0-9]+\([^)]*\)\s*{[^}]*deactivate[^}]*}/gi;
    const deactivateMatches = code.match(deactivatePattern);
    
    if (deactivateMatches) {
        console.log('\n找到停用相关函数:');
        deactivateMatches.forEach((match, index) => {
            console.log(`  停用函数 ${index + 1}: ${match.substring(0, 200)}...`);
        });
    }
}

// 4. 查找webview相关代码
function findWebviewCode(code) {
    console.log('\n--- Webview相关代码 ---');
    
    // 查找webview相关的字符串和函数
    const webviewPatterns = [
        /webview/gi,
        /postMessage/gi,
        /onDidReceiveMessage/gi,
        /createWebviewPanel/gi,
        /html/gi
    ];
    
    webviewPatterns.forEach((pattern, index) => {
        const matches = code.match(pattern);
        if (matches) {
            console.log(`Webview模式 ${index + 1} (${pattern}): ${matches.length} 次匹配`);
        }
    });
    
    // 查找可能的HTML内容
    const htmlPattern = /<[^>]+>/g;
    const htmlMatches = code.match(htmlPattern);
    if (htmlMatches) {
        console.log('\n可能的HTML标签:');
        [...new Set(htmlMatches)].slice(0, 20).forEach((tag, index) => {
            console.log(`  ${index + 1}: ${tag}`);
        });
    }
}

// 5. 查找命令注册
function findCommandRegistration(code) {
    console.log('\n--- 命令注册分析 ---');
    
    // 查找命令相关的字符串
    const commandStrings = [
        'augment-infinity.inputCDK',
        'augment-infinity.activate',
        'registerCommand',
        'executeCommand'
    ];
    
    commandStrings.forEach(cmdStr => {
        if (code.includes(cmdStr)) {
            console.log(`发现命令字符串: "${cmdStr}"`);
            
            // 查找包含这个字符串的上下文
            const index = code.indexOf(cmdStr);
            if (index !== -1) {
                const start = Math.max(0, index - 100);
                const end = Math.min(code.length, index + cmdStr.length + 100);
                const context = code.substring(start, end);
                console.log(`  上下文: ...${context}...`);
            }
        }
    });
}

// 6. 查找可能的恶意代码模式
function findSuspiciousCode(code) {
    console.log('\n--- 可疑代码模式 ---');
    
    // 查找可能的数据收集
    const suspiciousPatterns = [
        /machine.*id/gi,
        /node-machine-id/gi,
        /getMachineId/gi,
        /process\.env/gi,
        /os\.hostname/gi,
        /os\.userInfo/gi,
        /fs\.readFile/gi,
        /fs\.writeFile/gi,
        /child_process/gi,
        /exec/gi,
        /spawn/gi
    ];
    
    suspiciousPatterns.forEach((pattern, index) => {
        const matches = code.match(pattern);
        if (matches) {
            console.log(`可疑模式 ${index + 1} (${pattern}):`, [...new Set(matches)]);
        }
    });
    
    // 查找require调用
    const requirePattern = /require\s*\(\s*['"]([^'"]+)['"]\s*\)/g;
    const requireMatches = [...code.matchAll(requirePattern)];
    if (requireMatches.length > 0) {
        console.log('\nRequire调用:');
        requireMatches.forEach((match, index) => {
            console.log(`  ${index + 1}: require('${match[1]}')`);
        });
    }
}

// 7. 尝试简单的字符串替换去混淆
function attemptSimpleDeobfuscation(code) {
    console.log('\n--- 尝试简单去混淆 ---');
    
    let deobfuscated = code;
    
    // 替换一些明显的混淆模式
    const replacements = [
        // 替换常见的字符串访问模式
        [/_0x[a-f0-9]+\(0x[a-f0-9]+,'[^']+'\)/g, '"OBFUSCATED_STRING"'],
        // 替换函数调用模式
        [/_0x[a-f0-9]+\[_0x[a-f0-9]+\(0x[a-f0-9]+,'[^']+'\)\]/g, 'OBFUSCATED_CALL'],
    ];
    
    replacements.forEach(([pattern, replacement], index) => {
        const beforeLength = deobfuscated.length;
        deobfuscated = deobfuscated.replace(pattern, replacement);
        const afterLength = deobfuscated.length;
        
        if (beforeLength !== afterLength) {
            console.log(`替换模式 ${index + 1}: 进行了 ${(beforeLength - afterLength) / (replacement.length - pattern.toString().length)} 次替换`);
        }
    });
    
    // 保存部分去混淆的代码
    const outputPath = 'partially_deobfuscated.js';
    fs.writeFileSync(outputPath, deobfuscated);
    console.log(`部分去混淆的代码已保存到: ${outputPath}`);
    
    return deobfuscated;
}

// 执行所有分析
const strings = extractStringArray(obfuscatedCode);
findMainExports(obfuscatedCode);
findActivationFunctions(obfuscatedCode);
findWebviewCode(obfuscatedCode);
findCommandRegistration(obfuscatedCode);
findSuspiciousCode(obfuscatedCode);
const partiallyDeobfuscated = attemptSimpleDeobfuscation(obfuscatedCode);

console.log('\n=== 手动去混淆完成 ===');
