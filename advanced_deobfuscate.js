const fs = require('fs');

// 读取混淆的代码
const obfuscatedCode = fs.readFileSync('extension/dist/extension.js', 'utf8');

console.log('=== 高级去混淆分析 ===');

// 1. 分析代码的基本结构
function analyzeCodeStructure(code) {
    console.log('\n--- 代码结构分析 ---');
    
    // 查找所有函数定义
    const functionMatches = code.match(/function\s+[a-zA-Z_$][a-zA-Z0-9_$]*\s*\([^)]*\)/g);
    if (functionMatches) {
        console.log('发现函数定义数量:', functionMatches.length);
        functionMatches.slice(0, 10).forEach((func, index) => {
            console.log(`  函数 ${index + 1}: ${func}`);
        });
    }
    
    // 查找变量声明
    const varMatches = code.match(/var\s+[a-zA-Z_$][a-zA-Z0-9_$]*\s*=/g);
    if (varMatches) {
        console.log('\n变量声明数量:', varMatches.length);
        // 显示前20个变量
        const uniqueVars = [...new Set(varMatches)];
        uniqueVars.slice(0, 20).forEach((varDecl, index) => {
            console.log(`  变量 ${index + 1}: ${varDecl}`);
        });
    }
}

// 2. 查找字符串和数字常量
function findConstants(code) {
    console.log('\n--- 常量分析 ---');
    
    // 查找字符串常量
    const stringMatches = code.match(/'[^']*'/g);
    if (stringMatches) {
        const uniqueStrings = [...new Set(stringMatches)];
        console.log('字符串常量数量:', uniqueStrings.length);
        console.log('前20个字符串:');
        uniqueStrings.slice(0, 20).forEach((str, index) => {
            console.log(`  ${index + 1}: ${str}`);
        });
    }
    
    // 查找十六进制数字
    const hexMatches = code.match(/0x[a-fA-F0-9]+/g);
    if (hexMatches) {
        const uniqueHex = [...new Set(hexMatches)];
        console.log('\n十六进制数字数量:', uniqueHex.length);
        console.log('前20个十六进制数字:');
        uniqueHex.slice(0, 20).forEach((hex, index) => {
            console.log(`  ${index + 1}: ${hex} (${parseInt(hex, 16)})`);
        });
    }
}

// 3. 查找可疑的API调用和模式
function findSuspiciousPatterns(code) {
    console.log('\n--- 可疑模式分析 ---');
    
    // 查找可能的网络相关代码
    const networkPatterns = [
        /http[s]?:\/\/[^\s'"]+/gi,
        /fetch\s*\(/gi,
        /XMLHttpRequest/gi,
        /\.send\s*\(/gi,
        /\.post\s*\(/gi,
        /\.get\s*\(/gi,
        /ajax/gi
    ];
    
    networkPatterns.forEach((pattern, index) => {
        const matches = code.match(pattern);
        if (matches) {
            console.log(`网络模式 ${index + 1}:`, matches);
        }
    });
    
    // 查找文件系统操作
    const fsPatterns = [
        /fs\.[a-zA-Z]+/gi,
        /readFile/gi,
        /writeFile/gi,
        /existsSync/gi,
        /mkdirSync/gi
    ];
    
    console.log('\n文件系统操作:');
    fsPatterns.forEach((pattern, index) => {
        const matches = code.match(pattern);
        if (matches) {
            console.log(`FS模式 ${index + 1}:`, [...new Set(matches)]);
        }
    });
    
    // 查找进程和系统相关
    const systemPatterns = [
        /process\.[a-zA-Z]+/gi,
        /child_process/gi,
        /exec/gi,
        /spawn/gi,
        /os\.[a-zA-Z]+/gi
    ];
    
    console.log('\n系统操作:');
    systemPatterns.forEach((pattern, index) => {
        const matches = code.match(pattern);
        if (matches) {
            console.log(`系统模式 ${index + 1}:`, [...new Set(matches)]);
        }
    });
}

// 4. 尝试提取可读的字符串
function extractReadableStrings(code) {
    console.log('\n--- 可读字符串提取 ---');
    
    // 查找可能包含有意义信息的字符串
    const meaningfulStrings = code.match(/'[a-zA-Z][a-zA-Z0-9\s\-_./:]{3,}'/g);
    if (meaningfulStrings) {
        const filtered = meaningfulStrings.filter(str => {
            const content = str.slice(1, -1); // 移除引号
            return content.length > 3 && 
                   !/^[a-f0-9]+$/i.test(content) && // 不是纯十六进制
                   !/^[0-9]+$/.test(content); // 不是纯数字
        });
        
        console.log('有意义的字符串:');
        [...new Set(filtered)].slice(0, 30).forEach((str, index) => {
            console.log(`  ${index + 1}: ${str}`);
        });
    }
}

// 5. 查找VS Code扩展相关的模式
function findVSCodePatterns(code) {
    console.log('\n--- VS Code扩展模式 ---');
    
    const vscodePatterns = [
        /vscode/gi,
        /extension/gi,
        /activate/gi,
        /deactivate/gi,
        /command/gi,
        /webview/gi,
        /panel/gi,
        /workspace/gi,
        /window/gi,
        /registerCommand/gi
    ];
    
    vscodePatterns.forEach((pattern, index) => {
        const matches = code.match(pattern);
        if (matches && matches.length > 0) {
            console.log(`VS Code模式 ${index + 1} (${pattern}):`, matches.length, '次匹配');
        }
    });
}

// 执行分析
analyzeCodeStructure(obfuscatedCode);
findConstants(obfuscatedCode);
findSuspiciousPatterns(obfuscatedCode);
extractReadableStrings(obfuscatedCode);
findVSCodePatterns(obfuscatedCode);

console.log('\n=== 分析完成 ===');
