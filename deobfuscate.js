const fs = require('fs');

// 读取混淆的代码
const obfuscatedCode = fs.readFileSync('extension/dist/extension.js', 'utf8');

console.log('开始分析混淆代码...');
console.log('代码长度:', obfuscatedCode.length);

// 尝试提取字符串数组
function extractStringArray(code) {
    // 查找类似 _0x1158 = function() { return ['string1', 'string2', ...] } 的模式
    const arrayMatch = code.match(/_0x[a-f0-9]+\s*=\s*function\s*\(\s*\)\s*{\s*return\s*\[([^\]]+)\]/);
    if (arrayMatch) {
        try {
            // 提取字符串数组内容
            const arrayContent = arrayMatch[1];
            // 简单解析字符串（这里只是基础解析）
            const strings = arrayContent.match(/'[^']*'/g) || [];
            return strings.map(s => s.slice(1, -1)); // 移除引号
        } catch (e) {
            console.log('解析字符串数组失败:', e.message);
        }
    }
    return [];
}

// 查找主要的导出函数
function findMainExports(code) {
    // 查找 exports 或 module.exports 相关的代码
    const exportMatches = code.match(/(?:exports|module\.exports)\s*[=.][^;]+/g);
    return exportMatches || [];
}

// 查找VS Code相关的API调用
function findVSCodeAPIs(code) {
    const vscodePatterns = [
        /vscode\.[a-zA-Z.]+/g,
        /commands\.register[^;]+/g,
        /window\.(showInformationMessage|showErrorMessage|showWarningMessage)[^;]+/g,
        /workspace\.[a-zA-Z.]+/g
    ];

    const results = [];
    vscodePatterns.forEach(pattern => {
        const matches = code.match(pattern);
        if (matches) {
            results.push(...matches);
        }
    });
    return results;
}

// 查找网络请求
function findNetworkRequests(code) {
    const networkPatterns = [
        /https?:\/\/[^\s'"]+/g,
        /fetch\s*\([^)]+\)/g,
        /XMLHttpRequest/g,
        /\.post\s*\(/g,
        /\.get\s*\(/g
    ];

    const results = [];
    networkPatterns.forEach(pattern => {
        const matches = code.match(pattern);
        if (matches) {
            results.push(...matches);
        }
    });
    return results;
}

console.log('\n=== 字符串数组分析 ===');
const strings = extractStringArray(obfuscatedCode);
if (strings.length > 0) {
    console.log('提取到字符串数量:', strings.length);
    console.log('前20个字符串:');
    strings.slice(0, 20).forEach((str, index) => {
        console.log(`  ${index}: "${str}"`);
    });
} else {
    console.log('未找到明显的字符串数组');
}

console.log('\n=== 导出函数分析 ===');
const exportStatements = findMainExports(obfuscatedCode);
exportStatements.forEach(exp => {
    console.log('导出:', exp);
});

console.log('\n=== VS Code API调用分析 ===');
const vscodeAPIs = findVSCodeAPIs(obfuscatedCode);
if (vscodeAPIs.length > 0) {
    vscodeAPIs.forEach(api => {
        console.log('VS Code API:', api);
    });
} else {
    console.log('未发现明显的VS Code API调用');
}

console.log('\n=== 网络请求分析 ===');
const networkRequests = findNetworkRequests(obfuscatedCode);
if (networkRequests.length > 0) {
    networkRequests.forEach(req => {
        console.log('网络请求:', req);
    });
} else {
    console.log('未发现明显的网络请求');
}

// 查找require调用
const requireMatches = obfuscatedCode.match(/require\s*\(\s*['"][^'"]+['"]\s*\)/g);
if (requireMatches) {
    console.log('\n=== Require调用 ===');
    requireMatches.forEach(req => console.log('  -', req));
}

console.log('\n=== 分析完成 ===');
