{"name": "augment-infinity", "displayName": "augment infinity", "description": "稳定防封，正在使用中", "version": "2.0.1", "publisher": "menglong", "author_id": "we<PERSON>u", "icon": "resources/icon.png", "engines": {"vscode": "^1.23.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "augment-infinity-sidebar", "title": "augment infinity", "icon": "resources/icon.svg"}]}, "views": {"augment-infinity-sidebar": [{"id": "augment-infinity-activation", "name": "功能界面", "type": "webview"}]}, "commands": [{"command": "augment-infinity.inputCDK", "title": "输入 CDK"}, {"command": "augment-infinity.activate", "title": "激活软件"}]}, "scripts": {"vscode:prepublish": "npm run package", "compile": "npm run check-types && npm run lint && node esbuild.js", "watch": "node esbuild.js --watch", "package": "npm run check-types && npm run lint && node esbuild.js --production", "check-types": "tsc --noEmit", "lint": "eslint src", "vsce:package": "vsce package"}, "devDependencies": {"@types/node": "20.x", "@types/vscode": "^1.93.1", "@typescript-eslint/eslint-plugin": "^8.28.0", "@typescript-eslint/parser": "^8.28.0", "@vscode/vsce": "^2.24.0", "esbuild": "^0.25.1", "eslint": "^9.23.0", "typescript": "^5.8.2"}, "dependencies": {"node-machine-id": "^1.1.12"}}