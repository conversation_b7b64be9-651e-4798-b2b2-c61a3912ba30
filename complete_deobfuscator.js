const fs = require('fs');

// 读取混淆的代码
const obfuscatedCode = fs.readFileSync('extension/dist/extension.js', 'utf8');

console.log('=== 完全解密分析 ===');

// 1. 提取字符串解密函数
function extractDecryptionFunction(code) {
    console.log('\n--- 提取解密函数 ---');
    
    // 查找主要的解密函数 _0x55f7
    const decryptFunctionMatch = code.match(/function\s+_0x55f7\([^)]+\)\s*{[^}]+}/);
    if (decryptFunctionMatch) {
        console.log('找到主解密函数:');
        console.log(decryptFunctionMatch[0]);
        
        // 尝试提取解密逻辑
        const functionBody = decryptFunctionMatch[0];
        return functionBody;
    }
    
    return null;
}

// 2. 提取字符串数组
function extractStringArrays(code) {
    console.log('\n--- 提取字符串数组 ---');
    
    // 查找所有可能的字符串数组
    const arrayPatterns = [
        /\[['"][^'"]*['"](?:,\s*['"][^'"]*['"])*\]/g,
        /_0x[a-f0-9]+\s*=\s*\[[^\]]+\]/g,
        /return\s*\[[^\]]+\]/g
    ];
    
    const foundArrays = [];
    arrayPatterns.forEach((pattern, index) => {
        const matches = code.match(pattern);
        if (matches) {
            console.log(`字符串数组模式 ${index + 1}:`, matches.length, '个匹配');
            foundArrays.push(...matches);
        }
    });
    
    return foundArrays;
}

// 3. 分析混淆模式
function analyzeObfuscationPattern(code) {
    console.log('\n--- 混淆模式分析 ---');
    
    // 查找变量名模式
    const variablePattern = /_0x[a-f0-9]+/g;
    const variables = code.match(variablePattern);
    if (variables) {
        const uniqueVars = [...new Set(variables)];
        console.log('混淆变量数量:', uniqueVars.length);
        console.log('前20个变量:', uniqueVars.slice(0, 20));
    }
    
    // 查找函数调用模式
    const functionCallPattern = /_0x[a-f0-9]+\([^)]*\)/g;
    const functionCalls = code.match(functionCallPattern);
    if (functionCalls) {
        console.log('混淆函数调用数量:', functionCalls.length);
        console.log('前10个调用:', functionCalls.slice(0, 10));
    }
    
    // 查找字符串访问模式
    const stringAccessPattern = /_0x[a-f0-9]+\(0x[a-f0-9]+,\s*['"][^'"]*['"]\)/g;
    const stringAccesses = code.match(stringAccessPattern);
    if (stringAccesses) {
        console.log('字符串访问模式数量:', stringAccesses.length);
        console.log('前10个访问:', stringAccesses.slice(0, 10));
    }
}

// 4. 尝试手动解密部分字符串
function attemptStringDecryption(code) {
    console.log('\n--- 尝试字符串解密 ---');
    
    // 查找可能的字符串映射
    const stringMappings = new Map();
    
    // 查找明显的字符串赋值
    const assignmentPattern = /['"]([^'"]{3,})['"]/g;
    const strings = [...code.matchAll(assignmentPattern)];
    
    console.log('发现的明文字符串:');
    strings.slice(0, 50).forEach((match, index) => {
        const str = match[1];
        if (str.length > 3 && !/^[a-f0-9]+$/i.test(str)) {
            console.log(`  ${index + 1}: "${str}"`);
            stringMappings.set(index, str);
        }
    });
    
    return stringMappings;
}

// 5. 查找关键功能代码
function findKeyFunctionality(code) {
    console.log('\n--- 关键功能识别 ---');
    
    // VS Code相关API
    const vscodeAPIs = [
        'vscode.commands.registerCommand',
        'vscode.window.createWebviewPanel',
        'vscode.window.showInformationMessage',
        'vscode.window.showErrorMessage',
        'vscode.workspace',
        'context.subscriptions.push'
    ];
    
    vscodeAPIs.forEach(api => {
        if (code.includes(api)) {
            console.log(`发现API: ${api}`);
            // 查找包含此API的上下文
            const index = code.indexOf(api);
            const start = Math.max(0, index - 200);
            const end = Math.min(code.length, index + api.length + 200);
            const context = code.substring(start, end);
            console.log(`  上下文: ...${context.replace(/\s+/g, ' ')}...`);
        }
    });
    
    // 查找require调用
    const requirePattern = /require\s*\(\s*['"]([^'"]+)['"]\s*\)/g;
    const requires = [...code.matchAll(requirePattern)];
    console.log('\nRequire调用:');
    requires.forEach((match, index) => {
        console.log(`  ${index + 1}: require('${match[1]}')`);
    });
    
    // 查找可能的命令字符串
    const commandStrings = [
        'augment-infinity.inputCDK',
        'augment-infinity.activate',
        'inputCDK',
        'activate',
        'command',
        'webview',
        'postMessage'
    ];
    
    console.log('\n命令字符串搜索:');
    commandStrings.forEach(cmd => {
        if (code.includes(cmd)) {
            console.log(`发现命令: "${cmd}"`);
        }
    });
}

// 6. 尝试重构部分代码
function attemptCodeReconstruction(code) {
    console.log('\n--- 代码重构尝试 ---');
    
    let reconstructed = code;
    
    // 替换一些明显的模式
    const replacements = [
        // 替换常见的混淆函数名
        [/_0x55f7/g, 'decryptString'],
        [/function\s+_0x[a-f0-9]+/g, 'function obfuscatedFunction'],
        [/var\s+_0x[a-f0-9]+\s*=/g, 'var obfuscatedVar ='],
        
        // 替换一些明显的字符串
        [/'vscode'/g, '"vscode"'],
        [/'require'/g, '"require"'],
        [/'exports'/g, '"exports"'],
        [/'module'/g, '"module"']
    ];
    
    replacements.forEach(([pattern, replacement], index) => {
        const beforeLength = reconstructed.length;
        reconstructed = reconstructed.replace(pattern, replacement);
        const afterLength = reconstructed.length;
        
        if (beforeLength !== afterLength) {
            console.log(`替换 ${index + 1}: 进行了替换`);
        }
    });
    
    // 保存重构的代码
    const outputPath = 'reconstructed_code.js';
    fs.writeFileSync(outputPath, reconstructed);
    console.log(`重构代码已保存到: ${outputPath}`);
    
    return reconstructed;
}

// 7. 深度分析模块结构
function analyzeModuleStructure(code) {
    console.log('\n--- 模块结构分析 ---');
    
    // 查找模块定义模式
    const modulePatterns = [
        /function\([^)]*\)\s*{\s*var\s+[^=]+=\s*[^;]+;/g,
        /\[function\([^)]*\)\s*{[^}]+}/g,
        /exports\s*=\s*{[^}]*}/g
    ];
    
    modulePatterns.forEach((pattern, index) => {
        const matches = code.match(pattern);
        if (matches) {
            console.log(`模块模式 ${index + 1}:`, matches.length, '个匹配');
            matches.slice(0, 3).forEach((match, matchIndex) => {
                console.log(`  模块 ${matchIndex + 1}:`, match.substring(0, 100) + '...');
            });
        }
    });
    
    // 查找可能的入口点
    const entryPatterns = [
        /activate\s*:\s*function/g,
        /deactivate\s*:\s*function/g,
        /exports\.activate/g,
        /module\.exports/g
    ];
    
    console.log('\n入口点搜索:');
    entryPatterns.forEach((pattern, index) => {
        const matches = code.match(pattern);
        if (matches) {
            console.log(`入口模式 ${index + 1}:`, matches);
        }
    });
}

// 执行所有分析
console.log('开始完全解密分析...');
console.log('代码长度:', obfuscatedCode.length);

const decryptFunction = extractDecryptionFunction(obfuscatedCode);
const stringArrays = extractStringArrays(obfuscatedCode);
analyzeObfuscationPattern(obfuscatedCode);
const stringMappings = attemptStringDecryption(obfuscatedCode);
findKeyFunctionality(obfuscatedCode);
const reconstructedCode = attemptCodeReconstruction(obfuscatedCode);
analyzeModuleStructure(obfuscatedCode);

console.log('\n=== 完全解密分析完成 ===');
